# Loader Component

A beautiful, animated loader component for the HERBIT project. This loader replaces all previous loading indicators with a consistent, modern design.

## Features

- Smooth, animated SVG loader with gradient effects
- Customizable size and message
- Responsive design
- Optional centering
- Consistent styling with the app theme

## Usage

### Basic Usage

```vue
<template>
  <Loader />
</template>

<script setup>
import { Loader } from '@/components/ui/loader';
</script>
```

### With Custom Size and Message

```vue
<template>
  <Loader 
    :size="96" 
    message="Loading data..."
    :centered="true"
  />
</template>
```

### In Loading States

```vue
<template>
  <div v-if="isLoading" class="py-12">
    <Loader 
      :size="80" 
      message="Loading skills..."
      :centered="true"
    />
  </div>
  
  <div v-else>
    <!-- Your content here -->
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Loader } from '@/components/ui/loader';

const isLoading = ref(true);
</script>
```

### In Buttons (using Button component's loading prop)

```vue
<template>
  <Button 
    :loading="isSubmitting"
    @click="handleSubmit"
  >
    Submit
  </Button>
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | String \| Number | `128` | Size of the loader in pixels |
| `message` | String | `''` | Optional loading message to display |
| `containerClass` | String | `''` | Additional CSS classes for the container |
| `messageClass` | String | `''` | Additional CSS classes for the message |
| `centered` | Boolean | `true` | Whether to center the loader vertically |

## Examples

### Small Loader for Buttons
```vue
<Loader :size="20" :centered="false" />
```

### Medium Loader for Cards
```vue
<Loader :size="64" message="Loading..." />
```

### Large Loader for Full Page
```vue
<Loader :size="128" message="Loading application..." />
```

### Custom Styling
```vue
<Loader 
  :size="80"
  message="Please wait..."
  container-class="my-custom-container"
  message-class="text-blue-400 font-bold"
/>
```

## Migration from Old Loading Indicators

### Before (Old SpinnerIcon)
```vue
<SpinnerIcon :size="20" class="mr-2" />
```

### After (New Loader)
```vue
<Loader :size="20" :centered="false" />
```

### Before (Custom Spinner)
```vue
<div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue" />
```

### After (New Loader)
```vue
<Loader :size="80" />
```

## Integration with Composables

The Loader works seamlessly with the existing `useLoadingState` composable:

```vue
<template>
  <div v-if="isLoading">
    <Loader 
      :size="80" 
      :message="loadingMessage"
      :centered="true"
    />
  </div>
</template>

<script setup>
import { useLoadingState } from '@/composables';
import { Loader } from '@/components/ui/loader';

const { isLoading, loadingMessage } = useLoadingState();
</script>
```
