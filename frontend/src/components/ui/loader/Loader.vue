<template>
  <div class="loader-container" :class="[containerClass, { 'centered': centered }]">
    <svg
      class="loader"
      viewBox="0 0 128 128"
      :style="{ width: normalizedSize, height: normalizedSize }"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient :id="`grad1-${uniqueId}`" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#000" />
          <stop offset="40%" stop-color="#fff" />
          <stop offset="100%" stop-color="#fff" />
        </linearGradient>
        <linearGradient :id="`grad2-${uniqueId}`" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#000" />
          <stop offset="60%" stop-color="#000" />
          <stop offset="100%" stop-color="#fff" />
        </linearGradient>
        <mask :id="`mask1-${uniqueId}`">
          <rect x="0" y="0" width="128" height="128" :fill="`url(#grad1-${uniqueId})`" />
        </mask>
        <mask :id="`mask2-${uniqueId}`">
          <rect x="0" y="0" width="128" height="128" :fill="`url(#grad2-${uniqueId})`" />
        </mask>
      </defs>
      <g fill="none" stroke-linecap="round" stroke-width="8">
        <circle class="loader_ring" r="56" cx="64" cy="64" stroke="transparent" />
        <g stroke="#3b82f6">
          <path
            class="loader_worm1"
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke="#ef4444"
            stroke-dasharray="43.98 307.87"
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke="#8b5cf6"
                stroke-dasharray="43.98 175.92"
              />
            </g>
          </g>
        </g>
        <g stroke="hsl(283,90%,50%)" :mask="`url(#mask1-${uniqueId})`">
          <path
            class="loader_worm1"
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke-dasharray="43.98 307.87"
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke-dasharray="43.98 175.92"
              />
            </g>
          </g>
        </g>
        <g stroke="hsl(343,90%,50%)" :mask="`url(#mask2-${uniqueId})`">
          <path 
            class="loader_worm1" 
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke-dasharray="43.98 307.87" 
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path 
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke-dasharray="43.98 175.92" 
              />
            </g>
          </g>
        </g>
      </g>
    </svg>
    
    <!-- Optional loading message -->
    <span v-if="message" class="loader-message" :class="messageClass">
      {{ message }}
    </span>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

// Generate unique IDs for SVG elements to avoid conflicts
const uniqueId = ref(Math.random().toString(36).substring(2, 11));

const props = defineProps({
  size: {
    type: [String, Number],
    default: 128,
  },
  message: {
    type: String,
    default: ''
  },
  containerClass: {
    type: String,
    default: ''
  },
  messageClass: {
    type: String,
    default: ''
  },
  centered: {
    type: Boolean,
    default: true
  }
});

// Normalize size to include 'px' if not present
const normalizedSize = computed(() => {
  const size = props.size;
  if (typeof size === 'number') {
    return `${size}px`;
  }
  return /^\d+$/.test(size) ? `${size}px` : size;
});
</script>

<style scoped>
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loader-container.centered {
  justify-content: center;
  min-height: 200px;
}

.loader {
  --dur: 2s;
  display: block;
  max-width: 100%;
  height: auto;
}

.loader-message {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Animation keyframes */
@keyframes loader-flip {
  from {
    transform: rotate(-180deg);
  }
  24%,
  to {
    transform: rotate(0deg);
  }
}

.loader_worm1,
.loader_worm2,
.loader_worm2-1 {
  animation: loader-flip var(--dur) infinite cubic-bezier(0.83, 0, 0.17, 1);
  transform-origin: center;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg: hsl(var(--hue), 90%, 10%);
    --fg: hsl(var(--hue), 90%, 90%);
  }
}
</style>
