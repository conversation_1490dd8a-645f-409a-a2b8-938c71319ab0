<template>
  <div class="loader-container" :class="[containerClass, { 'centered': centered }]">
    <svg 
      class="loader" 
      viewBox="0 0 128 128" 
      :width="normalizedSize" 
      :height="normalizedSize" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="grad1" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#000" />
          <stop offset="40%" stop-color="#fff" />
          <stop offset="100%" stop-color="#fff" />
        </linearGradient>
        <linearGradient id="grad2" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#000" />
          <stop offset="60%" stop-color="#000" />
          <stop offset="100%" stop-color="#fff" />
        </linearGradient>
        <mask id="mask1">
          <rect x="0" y="0" width="128" height="128" fill="url(#grad1)" />
        </mask>
        <mask id="mask2">
          <rect x="0" y="0" width="128" height="128" fill="url(#grad2)" />
        </mask>
      </defs>
      <g fill="none" stroke-linecap="round" stroke-width="16">
        <circle class="loader_ring" r="56" cx="64" cy="64" stroke="transparent" />
        <g stroke="hsl(223,90%,50%)">
          <path 
            class="loader_worm1" 
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke="hsl(343,90%,50%)" 
            stroke-dasharray="43.98 307.87" 
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path 
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke-dasharray="43.98 175.92" 
              />
            </g>
          </g>
        </g>
        <g stroke="hsl(283,90%,50%)" mask="url(#mask1)">
          <path 
            class="loader_worm1" 
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke-dasharray="43.98 307.87" 
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path 
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke-dasharray="43.98 175.92" 
              />
            </g>
          </g>
        </g>
        <g stroke="hsl(343,90%,50%)" mask="url(#mask2)">
          <path 
            class="loader_worm1" 
            d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
            stroke-dasharray="43.98 307.87" 
          />
          <g transform="translate(42,42)">
            <g class="loader_worm2" transform="translate(-42,0)">
              <path 
                class="loader_worm2-1"
                d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
                stroke-dasharray="43.98 175.92" 
              />
            </g>
          </g>
        </g>
      </g>
    </svg>
    
    <!-- Optional loading message -->
    <span v-if="message" class="loader-message" :class="messageClass">
      {{ message }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  size: {
    type: [String, Number],
    default: 128,
  },
  message: {
    type: String,
    default: ''
  },
  containerClass: {
    type: String,
    default: ''
  },
  messageClass: {
    type: String,
    default: ''
  },
  centered: {
    type: Boolean,
    default: true
  }
});

// Normalize size to include 'px' if not present
const normalizedSize = computed(() => {
  const size = props.size;
  if (typeof size === 'number') {
    return `${size}px`;
  }
  return /^\d+$/.test(size) ? `${size}px` : size;
});
</script>

<style scoped>
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loader-container.centered {
  justify-content: center;
  min-height: 200px;
}

.loader {
  --dur: 2s;
  display: block;
  width: v-bind('normalizedSize');
  height: v-bind('normalizedSize');
}

.loader-message {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Animation keyframes */
@keyframes loader-flip {
  from {
    transform: translate(13.75px, 9.25px) rotate(-180deg);
  }
  24%,
  to {
    transform: translate(13.75px, 9.25px) rotate(0);
  }
}

.loader_worm1,
.loader_worm2,
.loader_worm2-1 {
  animation: loader-flip var(--dur) infinite cubic-bezier(0.83, 0, 0.17, 1);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg: hsl(var(--hue), 90%, 10%);
    --fg: hsl(var(--hue), 90%, 90%);
  }
}
</style>
