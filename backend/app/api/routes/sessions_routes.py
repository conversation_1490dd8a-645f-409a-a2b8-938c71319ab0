"""
Sessions-related API routes for the quiz/assessment management system.
"""

import os
from typing import Dict, Optional

import psycopg2
import psycopg2.extras
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...api.routes.assessment_routes import rate_limiter
from ...config.db_config import DATABASE_CONFIG
from ...models.assessment_manager import (
    calculate_total_score_for_assessment,
    get_performance_level_with_correct_total,
    get_session_and_assessment_details_by_code,
)
from ...models.sessions_manager import (
    build_session_filter_clauses,
    calculate_session_scores,
    complete_session_in_db,
    create_session_in_db,
    find_or_create_user_by_details,
    generate_unique_session_code,
    get_completed_session_for_results,
    get_or_create_user,
    get_session_answers_for_results,
    get_session_for_start_or_validation,
    get_session_user_details,
    get_sessions_by_user_id,
    get_sessions_count,
    get_sessions_data,
    get_single_session_data,
    get_user_id_by_email,
    handle_completed_session,
    handle_expired_session,
    start_session_in_db,
    validate_assessment_exists,
    validate_session_code_format,
)
from ...utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from ...utils.hashid_utils import (
    decode_assessment_id,
    decode_session_code,
    detect_hash_type,
)
from ...utils.logger import (
    debug,
    error,
    info,
    warning,
)

sessions_router = APIRouter()


class StartSessionRequest(BaseModel):
    session_code: str


class SessionCodeRequest(BaseModel):
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


# =============================================================================
# GET SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


@sessions_router.get("/admin/sessions")
async def get_sessions(
    limit: int = Query(3, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(
        None, description="Filter by session status: 'pending', 'completed', or 'all'"
    ),
):
    """
    Get sessions with pagination and optional status filtering

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
        status_filter: Filter by session status ('pending', 'completed', or 'all')
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Build WHERE clauses based on status filter
                where_clause, count_where_clause = build_session_filter_clauses(status_filter)

                # Get total count
                total = get_sessions_count(cur, count_where_clause)

                # Get paginated sessions data
                sessions = get_sessions_data(cur, where_clause, limit, offset)

                # Transform response to include hashed IDs
                hashed_sessions = hash_ids_in_response(sessions)

                # Return paginated response
                return paginated_response(
                    data=hashed_sessions,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Sessions retrieved successfully",
                    additional_data={"status_filter": status_filter or "all"},
                )
    except Exception as e:
        error(f"Error getting sessions: {str(e)}")
        return error_response(
            message=f"Error getting sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# =============================================================================
# GET SESSION DETAILS API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _enhance_completed_session_data(session_dict: dict) -> dict:
    """Add score and performance data for completed sessions."""
    if session_dict.get("status") == "completed" and session_dict.get("score") is not None:
        obtained_score = float(session_dict["score"])
        assessment_id = session_dict["assessment_id"]
        session_internal_id = session_dict["id"]

        # Calculate correct total possible score
        total_possible_score = calculate_total_score_for_assessment(assessment_id, session_internal_id)

        # Calculate performance level
        performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_internal_id)

        # Calculate percentage
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        # Add to session dict
        session_dict["obtained_score"] = obtained_score
        session_dict["total_possible_score"] = total_possible_score
        session_dict["percentage"] = round(percentage, 2)
        session_dict["performance_level"] = performance_level

    return session_dict


def _get_session_by_code(cur, session_id: str) -> Optional[Dict]:
    """Get session by 6-digit session code by calling the data fetcher."""
    info(f"Using 6-digit session code: {session_id}")
    where_clause = "WHERE s.code = %s"
    return get_single_session_data(cur, where_clause, (session_id,))


def _get_session_by_numeric_id(cur, session_id: str) -> Optional[Dict]:
    """Get session by numeric ID by calling the data fetcher."""
    actual_id = int(session_id)
    info(f"Using numeric session ID: {actual_id}")
    where_clause = "WHERE s.id = %s"
    return get_single_session_data(cur, where_clause, (actual_id,))


def _decode_and_get_session(cur, session_id: str) -> Optional[Dict]:
    """Decode hash and get session using the central data fetching function."""
    info(f"Attempting to decode hash: {session_id}")
    hash_type = detect_hash_type(session_id)
    info(f"Detected hash type: {hash_type}")

    decoded_id = decode_session_code(session_id)
    if decoded_id:
        info(f"Successfully decoded as session hash: {session_id} -> {decoded_id}")
    elif hash_type == "assessment":
        decoded_id = decode_assessment_id(session_id)
        if decoded_id:
            info(f"Successfully decoded as assessment hash (treating as session): {session_id} -> {decoded_id}")

    info(f"Final decode result: {decoded_id}")

    if not decoded_id:
        warning(f"Invalid or undecodable session hash: {session_id}")
        raise HTTPException(status_code=400, detail=f"Invalid session hash: {session_id}")

    info(f"Successfully decoded {session_id} to ID: {decoded_id}")
    where_clause = "WHERE s.id = %s"
    session_dict = get_single_session_data(cur, where_clause, (decoded_id,))

    if session_dict:
        # This post-processing is specific to the API response, so it stays here.
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()
        return session_dict

    return None


def _generate_session_error_message(session_id: str) -> str:
    """Generate appropriate error message based on session_id format."""
    if len(session_id) == 6 and session_id.isdigit():
        return f"Session with 6-digit code {session_id} not found"
    elif session_id.isdigit():
        return f"Session with numeric ID {session_id} not found"
    else:
        return f"Session with ID {session_id} not found. Expected: numeric ID or 6-digit session code"


def _prepare_session_response(session_dict: dict) -> dict:
    """Prepare session dictionary for response."""
    if session_dict and "id_hash" not in session_dict:
        # Enhance completed session data
        session_dict = _enhance_completed_session_data(session_dict)

        # Convert datetime objects to ISO strings before hash transformation
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()

        session_dict = hash_ids_in_response(session_dict)

    return session_dict


@sessions_router.get("/admin/sessions/{session_id}/details")
async def get_session_details_endpoint(session_id: str):
    """Get detailed session information by session ID (numeric ID or 6-digit session code)"""
    try:
        info(f"Session detail request for ID: {session_id} (type: {type(session_id)}, length: {len(session_id)})")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                session_dict = None

                # Check if it's a 6-digit session code first
                if len(session_id) == 6 and session_id.isdigit():
                    session_dict = _get_session_by_code(cur, session_id)
                # If it's a numeric ID (but not 6 digits), get directly by ID
                elif session_id.isdigit():
                    session_dict = _get_session_by_numeric_id(cur, session_id)
                else:
                    # Try to decode hash
                    session_dict = _decode_and_get_session(cur, session_id)

                if not session_dict:
                    error_detail = _generate_session_error_message(session_id)
                    warning(f"Session lookup failed: {error_detail}")
                    raise HTTPException(status_code=404, detail=error_detail)

                # Prepare session response
                session_dict = _prepare_session_response(session_dict)

                info(f"Returning session data keys: {list(session_dict.keys()) if session_dict else None}")

                return success_response(data=session_dict, message="Session details retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session details for ID {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting session details: {str(e)}")


# =============================================================================
# CREATE SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _process_user_identifier(identifier: str, default_email_domain: str) -> tuple[str, str, str]:
    """Process user identifier and return display_name, email, and external_id."""
    if "@" in identifier:
        email = identifier.lower()
        display_name = email.split("@")[0]
    else:
        display_name = identifier
        email = f"{display_name.lower()}@{default_email_domain}"

    external_id = display_name
    return display_name, email, external_id


def _validate_session_request(request_data: dict) -> tuple[int, list[str]]:
    """Validate session generation request and return assessment_id and user_identifiers."""
    assessment_id_str = request_data.get("assessment_id")
    usernames_str = request_data.get("usernames", "")

    if not assessment_id_str or not usernames_str:
        raise_http_exception(status_code=400, detail="Assessment ID and usernames are required.")

    try:
        assessment_id = int(assessment_id_str)
    except (ValueError, TypeError):
        raise_http_exception(status_code=400, detail="Assessment ID must be a valid number.")

    user_identifiers = [identifier.strip() for identifier in usernames_str.split(",") if identifier.strip()]

    if not user_identifiers:
        raise_http_exception(status_code=400, detail="No valid usernames provided.")

    return assessment_id, user_identifiers


def _process_single_user_session(cur, identifier: str, assessment_id: int, default_email_domain: str) -> dict:
    """Process a single user session creation."""
    # Process the identifier
    display_name, email, external_id = _process_user_identifier(identifier, default_email_domain)

    # Find or create the user in the database
    user_internal_id = find_or_create_user_by_details(
        cur,
        display_name=display_name,
        email=email,
        external_id=external_id,
    )

    # Generate a unique session code
    session_code = generate_unique_session_code(cur)

    # Insert the new session into the database
    session_db_id = create_session_in_db(cur, session_code, user_internal_id, assessment_id)

    return {
        "id": session_db_id,
        "username": display_name,
        "sessionCode": session_code,
        "sessionDbId": session_db_id,
    }


@sessions_router.post("/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """
    Generates assessment session codes for a list of users.
    It intelligently handles both usernames and email addresses as input.
    """
    try:
        # Validate request
        assessment_id, user_identifiers = _validate_session_request(request_data)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Validate assessment exists
                validate_assessment_exists(cur, assessment_id)

                created_sessions = []
                failed_sessions = []
                default_email_domain = os.getenv("DEFAULT_EMAIL_DOMAIN", "example.com")

                # Process each user identifier
                for identifier in user_identifiers:
                    try:
                        session_data = _process_single_user_session(
                            cur, identifier, assessment_id, default_email_domain
                        )
                        created_sessions.append(session_data)

                    except Exception as user_error:
                        error(f"Error creating session for identifier '{identifier}': {str(user_error)}")
                        failed_sessions.append(f"{identifier} (error: {str(user_error)})")

                conn.commit()

                # Prepare and return the final response
                if not created_sessions and failed_sessions:
                    raise_http_exception(
                        status_code=500, detail=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}"
                    )

                response_data = {"sessions": created_sessions}
                if failed_sessions:
                    response_data["warnings"] = f"Failed to create sessions for: {', '.join(failed_sessions)}"

                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message=f"Generated {len(created_sessions)} session codes successfully.",
                )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error(f"Unexpected error in generate_sessions: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail="An unexpected error occurred while generating sessions.")


# =============================================================================
# SUBMIT SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _validate_submit_session_request(session_code_input: str, user_id: str) -> str:
    """Validate submit session request parameters."""
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")
    if not user_id:
        raise_http_exception(status_code=400, detail="User ID is required.")

    session_code = validate_session_code_format(session_code_input)
    debug(f"Decoded session code: '{session_code}'")
    return session_code


def _get_session_details_for_submit(session_code: str) -> dict:
    """Get session details and validate they exist."""
    session_details = get_session_and_assessment_details_by_code(session_code)
    if not session_details:
        debug(f"Session not found for code: '{session_code}'")
        raise_http_exception(status_code=404, detail="Invalid or expired session code.")

    debug(
        f"Session details: status='{session_details['session_status']}', "
        f"session_id={session_details.get('session_id')}"
    )
    return session_details


def _validate_session_user(session_details: dict, user_id: str):
    """Validate that user matches the session."""
    internal_user_id = get_or_create_user(user_id)
    debug(f"User validation: internal_user_id={internal_user_id}, session_user_id={session_details.get('user_id')}")

    if session_details["user_id"] != internal_user_id:
        debug(
            f"User mismatch: session belongs to user_id={session_details['user_id']}, "
            f"but request from user_id={internal_user_id}"
        )
        raise_http_exception(status_code=403, detail="User does not match session.")


@sessions_router.post("/submit_session")
def submit_session(request: SessionSubmissionRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code_input = request.session_code
        user_id = request.user_id

        debug(f"Submit session request: session_code='{session_code_input}', user_id='{user_id}'")

        # Validate request parameters
        session_code = _validate_submit_session_request(session_code_input, user_id)

        # Get session details
        session_details = _get_session_details_for_submit(session_code)

        # Handle different session statuses
        current_status = session_details["session_status"]

        if current_status == "completed":
            return handle_completed_session(session_details)
        elif current_status == "expired":
            return handle_expired_session(session_details)
        elif current_status != "in_progress":
            debug(f"Session not in progress. Current status: {current_status}")
            raise_http_exception(
                status_code=400, detail=f"Session is not in progress. Current status: {current_status}"
            )

        # Validate user matches session
        _validate_session_user(session_details, user_id)

        # Calculate scores
        obtained_score, total_possible_score, performance_level = calculate_session_scores(session_details)

        # Complete session in database
        complete_session_in_db(session_details, obtained_score)

        # Calculate percentage and prepare response
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        data = {
            "session_id": session_details["session_id"],
            "obtained_score": obtained_score,
            "total_possible_score": total_possible_score,
            "percentage": round(percentage, 2),
            "performance_level": performance_level,
            "status": "completed",
        }
        hashed_data = hash_ids_in_response(data)
        return success_response(data=hashed_data, message="Session submitted successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error submitting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error submitting session: {str(e)}")


# =============================================================================
# VALIDATE SESSION API ENDPOINT (REFACTORED)
# =============================================================================
@sessions_router.post("/validate_session_code")
def validate_session_code(request: SessionCodeRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                session_details = get_session_for_start_or_validation(cur, session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # All details, including question_selection_mode, are now in one object
        response_data = {
            "session_id": session_details["id"],
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            # You may need to add remaining_time_seconds logic back here or in the data function
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "started_at": session_details["started_at"].isoformat() if session_details["started_at"] else None,
            "completed_at": session_details["completed_at"].isoformat() if session_details["completed_at"] else None,
            "question_selection_mode": session_details["question_selection_mode"],
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session code validated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# START SESSION API ENDPOINT
# =============================================================================
@sessions_router.post("/start_session")
def start_session(request: StartSessionRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # 1. Get session details to validate
                session_details = get_session_for_start_or_validation(cur, session_code)

                if not session_details:
                    raise_http_exception(status_code=404, detail="Invalid or expired session code.")

                if session_details["session_status"] != "pending":
                    raise_http_exception(
                        status_code=409,
                        detail=f"Session cannot be started. Current status: {session_details['session_status']}",
                    )

                # 2. Update the session in the database
                rows_updated = start_session_in_db(cur, session_details["id"])
                if rows_updated == 0:
                    raise_http_exception(status_code=500, detail="Failed to start session, status might have changed.")

                conn.commit()

                # 3. Refresh session details to get updated timestamps
                updated_session_details = get_session_for_start_or_validation(cur, session_code)

        # Format and return the response
        response_data = {
            "session_id": updated_session_details["id"],
            "session_code": session_code,
            "assessment_id": updated_session_details["assessment_id"],
            "assessment_name": updated_session_details["assessment_name"],
            "is_final": updated_session_details["is_final"],
            "username": updated_session_details.get("username", ""),
            "session_status": updated_session_details["session_status"],
            "started_at": (
                updated_session_details["started_at"].isoformat() if updated_session_details["started_at"] else None
            ),
            "completed_at": (
                updated_session_details["completed_at"].isoformat() if updated_session_details["completed_at"] else None
            ),
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session started successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# RESULT ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_id}/results")
async def get_session_results(session_id: str):
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # 1. Get the completed session details
                # Note: Assumes session_id can be a code or numeric ID. Hashing would be handled here if needed.
                session_dict = get_completed_session_for_results(cur, session_id)

                if not session_dict:
                    return error_response(
                        message="Completed session not found",
                        code=status.HTTP_404_NOT_FOUND,
                    )

                # 2. Get the detailed answers for that session
                answered_questions_raw = get_session_answers_for_results(cur, session_dict["id"])

        # The rest of the logic is for processing and formatting, which is fine to keep here.
        answered_questions, correct_answers, total_score = [], 0, 0
        for answer in answered_questions_raw:
            answer_dict = dict(answer)

            # Parse options if it's a JSON string
            options = answer_dict["options"]
            if isinstance(options, str):
                try:
                    import json

                    options = json.loads(options)
                except (json.JSONDecodeError, TypeError):
                    options = {}

            question_data = {
                "question": answer_dict["question"],
                "options": options,
                "userAnswer": answer_dict["user_answer"],
                "correctAnswerKey": answer_dict["correct_answer_key"],
                "isCorrect": answer_dict["is_correct"],
                "score": float(answer_dict["score"]) if answer_dict["score"] else 0,
                "level": answer_dict["level"],
            }

            answered_questions.append(question_data)

            if answer_dict["is_correct"]:
                correct_answers += 1

            total_score += question_data["score"]

        # Prepare response data (simplified for brevity)
        result_data = {
            "session_info": {
                "session_code": session_dict["session_code"],
                "username": session_dict["username"],
                "assessment_name": session_dict["assessment_name"],
                "completed_at": (session_dict["completed_at"].isoformat() if session_dict["completed_at"] else None),
                "question_selection_mode": session_dict["question_selection_mode"],
            },
            "score_summary": {
                "correct_answers": correct_answers,
                "total_questions": session_dict["total_questions"],
                "questions_attempted": len(answered_questions),
                "calculated_score": round(total_score, 2),
                "final_score": (
                    float(session_dict["score"]) if session_dict["score"] is not None else round(total_score, 2)
                ),
            },
            "answered_questions": answered_questions,
        }
        return success_response(data=result_data, message="Session results retrieved successfully")

    except Exception as e:
        error(f"Error getting session results for ID {session_id}: {str(e)}")
        return error_response(message=f"Error getting session results: {str(e)}", code=500)


# =============================================================================
# USER ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_code}/user")
async def get_session_user(session_code: str):
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise_http_exception(status_code=400, detail="Session code must be a 6-digit number")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # A single function call replaces three separate queries
                details = get_session_user_details(cur, session_code)

        if not details:
            raise_http_exception(status_code=404, detail="Session code not found")

        username = details["display_name"] or details["external_id"]

        response_data = {
            "username": username,
            "assessment_id": details["assessment_id"],
            "assessment_name": details["assessment_name"],
            "is_final": details["is_final"],
            "session_status": details["session_status"],
        }
        return hash_ids_in_response(response_data)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching session user: {str(e)}")


# =============================================================================
# SESSION ON USER EMAIL API ENDPOINT
# =============================================================================


@sessions_router.get("/user/{email}/sessions")
async def get_user_sessions_by_email(email: str):
    """Get all sessions for a specific user identified by email."""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # 1. Get the user's internal ID using the new data access function
                user_id = get_user_id_by_email(cur, email)

                if not user_id:
                    warning(f"User with email {email} not found in database")
                    # Return successfully with an empty list, matching original behavior
                    return success_response(
                        data={"sessions": [], "total": 0},
                        message=f"No user found with email {email}",
                    )

                # 2. Get all sessions for this user using the other new function
                sessions = get_sessions_by_user_id(cur, user_id)

        # 3. Post-processing and response formatting (outside the DB connection block)
        hashed_sessions = hash_ids_in_response(sessions)

        return success_response(
            data={
                "sessions": hashed_sessions,
                "total": len(sessions),
            },
            message="User sessions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting sessions for user {email}: {str(e)}", exc_info=True)
        # Use a more generic error message for the user
        raise_http_exception(status_code=500, detail="An unexpected error occurred while retrieving user sessions.")
